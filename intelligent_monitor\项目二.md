# 开发者文档：智能浏览器监控系统 (v8.0)

## 1. 项目概述

智能浏览器监控系统是一个基于 **UI Automation (UIA)** 和 **自适应策略管理** 的高级监控工具。它旨在通过模块化和智能化的方式，实时监控用户的浏览器活动，识别并自动处理不合规的网页内容。

其核心竞争力在于**多阶段生命周期管理器**，该管理器能够为每种浏览器（无论是标准版、修改版还是应用内嵌版）自动学习、缓存和应用最高效的URL信息获取策略和标签页关闭策略，实现了真正的“一次配置，自适应运行”。

**技术栈**:
*   **核心逻辑**: Python 3.6+
*   **UI交互**: `UIAComWrapper` (通过 `pythonnet` 访问 .NET 库)
*   **底层API**: `ctypes` (调用 Windows API)

## 2. 系统架构与模块分析

项目采用高内聚、低耦合的模块化设计，各模块职责分明，通过明确的接口进行协作。

```mermaid
graph TD
    subgraph "入口与调度 (main.py)"
        A[主监控循环]
    end

    subgraph "配置中心 (config.py)"
        B[配置参数]
    end

    subgraph "核心智能 (modules/managers.py)"
        C[StrategyLifecycleManager] -- 管理 --> D{浏览器档案}
        E[AIAuditManager] -- 管理 --> F{AI审核缓存}
    end

    subgraph "策略工具箱 (modules/)"
        G[browser_info_fetcher.py]
        H[tab_closer.py]
        I[blacklist_checker.py]
    end

    subgraph "底层依赖 (core/)"
        J[uia_base.py]
    end

    A -->|读取配置| B
    A -->|识别浏览器| G
    G -->|查询/更新状态| C
    A -->|决策| I
    A -->|AI审核| E
    A -- "触发关闭" --> H
    H -->|查询/更新状态| C
    G & H & E -->|读写缓存| D & F
    G & H & I -->|调用UIA/WinAPI| J
```

### 2.1. `main.py` - 主程序入口
*   **角色**: 系统的心跳和总指挥。
*   **核心职责**:
    1.  初始化 `StrategyLifecycleManager` 和 `AIAuditManager`。
    2.  进入主循环，每隔1.5秒获取当前前台窗口。
    3.  判断窗口是否为浏览器，若否则忽略。
    4.  调用 `get_browser_info_with_lifecycle()` 获取URL和标题。
    5.  执行**决策三部曲**:
        *   检查标题黑名单 (`check_title_against_blacklist`)。
        *   检查URL黑名单 (`check_url_against_blacklist`)。
        *   进行AI内容审核 (`ai_manager.check_and_audit`)。
    6.  若任一检查判定为“关闭”，则调用 `close_browser_tab()`。
    7.  打印详细的状态变化日志。

### 2.2. `config.py` - 全局配置模块
*   **角色**: 系统的控制面板。
*   **核心职责**:
    *   **调试开关**: `DEBUG_BLACKLIST`, `DEBUG_CACHE`, `DEBUG_LIFECYCLE`。
    *   **黑名单**: `BLACKLIST_KEYWORDS` (标题), `URL_BLACKLIST_KEYWORDS` (URL)。
    *   **AI审核配置**: `AI_AUDIT_CONFIG` 包括开关、API密钥、端点、模型和提示词模板。
    *   **生命周期参数**: `EXPLORATION_BUDGET`, `HARD_FAILURE_THRESHOLD`, `FULL_SEARCH_INTERVAL` 等核心调优参数。
    *   **常量定义**: `StrategyPhase`, `StrategyQuality` 等枚举类。

### 2.3. `modules/managers.py` - 核心管理器
这是系统的“大脑”，封装了所有状态管理和智能决策的复杂性。

*   **`StrategyLifecycleManager`**:
    *   **角色**: 浏览器策略的生命周期管理者与状态机。
    *   **核心职责**:
        *   为每个浏览器进程（如 `firefox.exe`）维护一个独立的**状态档案 (profile)**。
        *   统一管理该档案中的 **URL获取策略** 和 **标签页关闭策略** 的缓存。
        *   根据策略执行结果（成功、软失效、硬失效），驱动浏览器档案在 `EXPLORATION`, `STABLE`, `FAILED`, `ABANDONED` 四个阶段之间转换。
        *   通过 `should_perform_full_search()` 控制高成本的全量搜索操作，避免系统资源滥用。
        *   将所有浏览器档案持久化到 `browser_strategy_cache.json`。

*   **`AIAuditManager`**:
    *   **角色**: AI内容审核的缓存与异步处理中心。
    *   **核心职责**:
        *   管理AI审核结果的缓存，支持TTL（存活时间）。
        *   处理审核请求：优先从缓存读取，若无缓存则发起**异步**审核。
        *   通过 `pending_urls` 集合和线程锁 `threading.Lock` 防止对同一URL的重复请求。
        *   **注意**: 当前版本 `_perform_ai_request` 方法为**模拟实现**，需开发者自行替换为真实的API调用。

### 2.4. `modules/browser_info_fetcher.py` - 信息获取模块
*   **角色**: URL和标题的获取策略库与调度器。
*   **核心函数**:
    *   `get_browser_info_with_lifecycle()`: 调度函数。它不直接执行策略，而是查询 `StrategyLifecycleManager` 的状态，再决定是执行已缓存的单一高效策略，还是进行一轮完整的探索性搜索。
    *   `_get_url_firefox_robust()`: 专为Firefox设计的健壮策略，采用“锚点-验证”模型，先找到激活的Tab，再用Tab的标题去精准匹配对应的Document控件。
    *   `_get_url_chromium_generic()`: 适用于大多数Chromium内核浏览器的通用策略。

### 2.5. `modules/tab_closer.py` - 标签页关闭模块
*   **角色**: 标签页关闭的策略库与执行器。
*   **核心函数**:
    *   `close_browser_tab()`: 关闭逻辑的调度函数。优先尝试从 `StrategyLifecycleManager` 获取并执行缓存的关闭策略。如果缓存策略失效或不存在，则按预设顺序尝试所有可用策略。
    *   **策略瀑布流**:
        1.  `_strategy_internal_button`: 在Tab元素的子节点中找关闭按钮。
        2.  `_strategy_sibling_match`: 在Tab元素的兄弟节点中找关闭按钮。
        3.  `_strategy_indexed_match`: 当Tab和关闭按钮在不同容器但索引对应时使用。
        4.  `_strategy_proximity_match`: 基于屏幕坐标，寻找离Tab最近的关闭按钮。
        5.  `_strategy_ctrl_w`: 终极手段，模拟 `Ctrl+W` 键盘快捷键。

### 2.6. `core/uia_base.py` - 核心依赖封装
*   **角色**: 底层API的封装层。
*   **核心职责**:
    1.  安全地加载 `UIAComWrapper` 库，并处理加载失败的情况。
    2.  将常用的UIA类（如 `AutomationElement`, `ControlType`）导出，供上层模块使用。
    3.  封装常用的WinAPI函数和结构体（如 `GetForegroundWindow`, `SendInput`）。
    4.  提供基础辅助函数 `get_window_process_name_and_title()`。

## 3. 核心机制详解

### 3.1. URL获取策略的生命周期

这是系统最智能的部分。理解它对二次开发至关重要。

```mermaid
stateDiagram-v2
    [*] --> EXPLORATION: 新浏览器或重置后

    EXPLORATION --> STABLE: 找到高质量(HIGH)策略
    EXPLORATION --> ABANDONED: 预算耗尽,但找到次优(LOW)解
    EXPLORATION --> FAILED: 预算耗尽,未找到任何解

    STABLE --> STABLE: 策略成功或软失效(有标题无URL)
    STABLE --> FAILED: 硬失效(无标题无URL)达到阈值

    ABANDONED --> ABANDONED: (终端状态) 永久使用次优解

    FAILED --> EXPLORATION: 全局冷却(`FULL_SEARCH_INTERVAL`)结束后, 触发新一轮全量搜索
```

*   **阶段 (Phase)**:
    *   **EXPLORATION (探索)**: 初始状态。系统会尝试所有可用策略，目标是找到一个能同时获取URL和标题的**高质量(HIGH)**策略。此阶段受 `EXPLORATION_BUDGET` 限制。
    *   **STABLE (稳定)**: 最佳状态。已找到并缓存了高质量策略。后续调用将只执行这一个策略，性能最高。
    *   **ABANDONED (放弃)**: 优雅降级。探索预算用尽，但至少找到了一个能获取标题的**次优(LOW)**策略。系统会接纳这个次优解并停止探索，以节约资源。
    *   **FAILED (失效)**: 策略损坏。当稳定策略连续多次**硬失效**，或探索阶段一无所获时进入。此状态下，系统会暂停对该浏览器的尝试，直到全局搜索冷却结束。

*   **失效类型 (Failure Type)**:
    *   **硬失效 (Hard Failure)**: 连标题都获取不到。这被视为严重问题，会累加 `hard_failure_count` 计数器。
    *   **软失效 (Soft Failure)**: 能获取到标题，但URL为空。这被视为小问题，反而会**重置** `hard_failure_count`，因为这证明策略本身还部分有效。

### 3.2. 浏览器档案 (`browser_profile`) 数据结构

生命周期管理器为每个浏览器维护的所有信息都存储在此JSON对象中。

```json
{
  "firefox": {
    "phase": "stable",
    "exploration_attempts": 0,
    "hard_failure_count": 0,
    "strategy_name": "get_url_firefox_robust",
    "strategy_quality": "high",
    "close_strategy": {
        "name": "internal_button",
        "button": null 
    },
    "last_exploration_title": "Some Page",
    "best_low_quality_info": null,
    "created_at": "...",
    "last_updated": "..."
  }
}
```

*   `strategy_name`: 缓存的URL获取策略函数名。
*   `close_strategy`: **统一管理的关闭策略缓存**。包含了策略名和执行所需的参数（如`depth`）。

## 4. 如何扩展与修改

### 4.1. 添加一个新的URL获取策略

1.  **编写策略函数**: 在 `browser_info_fetcher.py` 中，创建一个新函数，例如 `_get_url_special_browser()`。
    *   函数接收 `window_element` 作为参数。
    *   必须返回 `(url, title)` 元组。任一值无法获取时，应返回 `None`。
2.  **注册策略**: 将新函数添加到 `URL_STRATEGIES` 字典中。
    ```python
    URL_STRATEGIES = {
        'get_url_firefox_robust': _get_url_firefox_robust,
        'get_url_chromium_generic': _get_url_chromium_generic,
        'get_url_special_browser': _get_url_special_browser, # <-- 添加在这里
    }
    ```
3.  **（可选）定向优化**: 在 `get_browser_info_with_lifecycle()` 中，如果新策略只适用于特定浏览器，可以像Firefox一样，将其优先插入到待尝试列表的开头。
    ```python
    strategies_to_try = [('get_url_chromium_generic', ...)]
    if "firefox" in process_name:
        strategies_to_try.insert(0, ('get_url_firefox_robust', ...))
    if "special_browser" in process_name:
        strategies_to_try.insert(0, ('get_url_special_browser', ...))
    ```

### 4.2. 添加一个新的标签页关闭策略

1.  **编写策略函数**: 在 `tab_closer.py` 中，创建一个新函数，例如 `_strategy_custom_find()`。
    *   函数签名应为 `def _strategy_custom_find(active_tab, **kwargs):`。
    *   如果找到关闭按钮，返回一个字典，如 `{'button': button_element}`。
    *   如果策略是直接执行动作（如键盘输入），返回一个包含 `method` 键的字典，如 `{'method': 'custom_action'}`。
    *   如果策略失败，返回 `None`。
2.  **注册策略**: 将新策略添加到 `CLOSE_STRATEGIES_TO_TRY` 列表中。**顺序很重要**，越精确、越可靠的策略应放得越靠前。
    ```python
    CLOSE_STRATEGIES_TO_TRY = [
        {'name': 'internal_button', 'function': _strategy_internal_button},
        # ... 其他策略
        {'name': 'custom_find', 'function': _strategy_custom_find}, # <-- 添加在这里
        {'name': 'ctrl_w', 'function': _strategy_ctrl_w},
    ]
    ```

### 4.3. 部署AI审核功能

1.  **设置环境变量**: 在您的系统中设置环境变量 `AI_API_KEY`，值为您的AI服务API密钥。这是推荐方式。或者，直接在 `config.py` 中修改 `AI_AUDIT_CONFIG['api_key']` 的默认值。
2.  **配置端点和模型**: 根据您使用的AI服务商，修改 `config.py` 中的 `api_endpoint` 和 `model`。
3.  **实现API调用**: 打开 `modules/managers.py`，找到 `_perform_ai_request` 方法。**删除或注释掉模拟代码**，并用实际的HTTP请求库（如 `requests`）实现API调用。
    ```python
    # _perform_ai_request in AIAuditManager

    # ...
    # TODO: 实际的AI API调用
    # 删除以下行:
    # time.sleep(5)
    # decision_text = random.choice(["allow", "deny"])

    # 添加真实实现:
    try:
        import requests
        response = requests.post(
            self.config['api_endpoint'],
            headers={'Authorization': f'Bearer {self.config["api_key"]}'},
            json={
                'model': self.config['model'],
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': 10 # 只需要很短的回复
            },
            timeout=15 # 设置超时
        )
        response.raise_for_status() # 检查HTTP错误
        decision_text = response.json()['choices'][0]['message']['content'].strip().lower()
    # ...
    ```

## 5. 调试技巧

*   要查看生命周期的详细决策过程，请在 `config.py` 中设置 `DEBUG_LIFECYCLE = True`。
*   要查看缓存的加载和保存日志，请设置 `DEBUG_CACHE = True`。
*   要查看黑名单匹配和标签页关闭的UI元素查找过程，请设置 `DEBUG_BLACKLIST = True`。
*   推荐在开发时同时打开这三个开关，以获得最全面的系统运行状态信息。